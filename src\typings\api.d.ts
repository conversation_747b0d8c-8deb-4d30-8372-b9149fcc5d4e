/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    /** 登录请求参数 */
    interface LoginParams {
      phone: string;
      password: string;
    }

    /** 登录响应数据 */
    interface LoginToken {
      /** 访问token */
      token: string;
      /** 刷新token */
      refreshToken: string;
    }

    /** 用户信息 */
    interface UserInfo {
      /** 用户ID */
      userId: string;
      /** 用户名 */
      userName: string;
      /** 用户角色列表 */
      roles: string[];
      /** 用户权限按钮列表 */
      buttons: string[];
    }

    /** 刷新token请求参数 */
    interface RefreshTokenParams {
      refreshToken: string;
    }

    /** 注册请求参数 */
    interface RegisterParams {
      /** 手机号 */
      phone: string;
      /** 密码 */
      password: string;
      /** 用户名 */
      username: string;
      /** 用户角色 */
      role: string;
    }

    /** 注册响应数据 */
    interface RegisterResult {
      /** 注册是否成功 */
      success: boolean;
      /** 消息 */
      message?: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }
}
