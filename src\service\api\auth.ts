import { request } from '../request';

/**
 * 登录接口
 *
 * @param params 登录参数
 */
export function fetchLogin(params: Api.Auth.LoginParams) {
  return request<Api.Auth.LoginToken>({
    url: '/api/auth/login',
    method: 'post',
    data: params
  });
}

/**
 * 登录接口（兼容旧版本）
 *
 * @param userName 用户名
 * @param password 密码
 */
export function fetchLoginLegacy(userName: string, password: string) {
  return fetchLogin({ userName, password });
}

/** 获取用户信息 */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/api/auth/getUserInfo' });
}

/**
 * 刷新token
 *
 * @param params 刷新token参数
 */
export function fetchRefreshToken(params: Api.Auth.RefreshTokenParams) {
  return request<Api.Auth.LoginToken>({
    url: '/api/auth/refreshToken',
    method: 'post',
    data: params
  });
}

/**
 * 刷新token（兼容旧版本）
 *
 * @param refreshToken 刷新token
 */
export function fetchRefreshTokenLegacy(refreshToken: string) {
  return fetchRefreshToken({ refreshToken });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
