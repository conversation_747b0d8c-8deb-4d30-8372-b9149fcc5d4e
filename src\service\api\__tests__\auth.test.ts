import { describe, it, expect, vi } from 'vitest';
import { fetchLogin, fetchGetUserInfo, fetchRefreshToken } from '../auth';

// Mock request module
vi.mock('../../request', () => ({
  request: vi.fn()
}));

describe('Auth API', () => {
  describe('fetchLogin', () => {
    it('应该接受正确的登录参数类型', () => {
      const loginParams: Api.Auth.LoginParams = {
        userName: 'testuser',
        password: 'testpass'
      };

      // 这应该通过TypeScript类型检查
      expect(() => fetchLogin(loginParams)).not.toThrow();
    });

    it('应该拒绝错误的参数类型', () => {
      // @ts-expect-error - 测试类型错误
      const invalidParams = {
        username: 'testuser', // 错误的属性名
        password: 'testpass'
      };

      // TypeScript应该在编译时捕获这个错误
      // fetchLogin(invalidParams);
    });
  });

  describe('fetchRefreshToken', () => {
    it('应该接受正确的刷新token参数类型', () => {
      const refreshParams: Api.Auth.RefreshTokenParams = {
        refreshToken: 'test-refresh-token'
      };

      expect(() => fetchRefreshToken(refreshParams)).not.toThrow();
    });
  });

  describe('fetchGetUserInfo', () => {
    it('应该返回正确的用户信息类型', () => {
      // 这个测试确保返回类型是正确的
      const userInfoPromise = fetchGetUserInfo();
      expect(userInfoPromise).toBeDefined();
    });
  });
});

// 类型安全性测试
describe('Type Safety Tests', () => {
  it('LoginParams 类型应该包含必需的字段', () => {
    const params: Api.Auth.LoginParams = {
      userName: 'test',
      password: 'test'
    };

    expect(params.userName).toBeDefined();
    expect(params.password).toBeDefined();
  });

  it('LoginToken 类型应该包含必需的字段', () => {
    const token: Api.Auth.LoginToken = {
      token: 'access-token',
      refreshToken: 'refresh-token'
    };

    expect(token.token).toBeDefined();
    expect(token.refreshToken).toBeDefined();
  });

  it('UserInfo 类型应该包含必需的字段', () => {
    const userInfo: Api.Auth.UserInfo = {
      userId: '123',
      userName: 'testuser',
      roles: ['admin'],
      buttons: ['create', 'edit']
    };

    expect(userInfo.userId).toBeDefined();
    expect(userInfo.userName).toBeDefined();
    expect(Array.isArray(userInfo.roles)).toBe(true);
    expect(Array.isArray(userInfo.buttons)).toBe(true);
  });
});
