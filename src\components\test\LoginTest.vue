<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { NCard, NForm, NFormItem, NInput, NButton, NSpace, NAlert, NCode, NLog } from 'naive-ui';
import { fetchLogin, fetchGetUserInfo } from '@/service/api/auth';

// 表单数据
const formData = reactive<Api.Auth.LoginParams>({
  userName: 'testuser',
  password: '123456'
});

// 状态管理
const loading = ref(false);
const loginResult = ref<Api.Auth.LoginToken | null>(null);
const userInfo = ref<Api.Auth.UserInfo | null>(null);
const errorMessage = ref('');
const logs = ref<string[]>([]);

// 添加日志
function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.push(`[${timestamp}] ${message}`);
}

// 清空日志
function clearLogs() {
  logs.value = [];
  loginResult.value = null;
  userInfo.value = null;
  errorMessage.value = '';
}

// 测试登录
async function testLogin() {
  loading.value = true;
  errorMessage.value = '';
  
  try {
    addLog('开始登录测试...');
    addLog(`请求参数: ${JSON.stringify(formData)}`);
    
    const { data, error } = await fetchLogin(formData);
    
    if (error) {
      errorMessage.value = `登录失败: ${error.message || '未知错误'}`;
      addLog(`登录失败: ${error.message || '未知错误'}`);
    } else if (data) {
      loginResult.value = data;
      addLog('登录成功!');
      addLog(`返回数据: ${JSON.stringify(data)}`);
      
      // 自动测试获取用户信息
      await testGetUserInfo();
    }
  } catch (err: any) {
    errorMessage.value = `登录异常: ${err.message || '网络错误'}`;
    addLog(`登录异常: ${err.message || '网络错误'}`);
  } finally {
    loading.value = false;
  }
}

// 测试获取用户信息
async function testGetUserInfo() {
  try {
    addLog('开始获取用户信息...');
    
    const { data, error } = await fetchGetUserInfo();
    
    if (error) {
      addLog(`获取用户信息失败: ${error.message || '未知错误'}`);
    } else if (data) {
      userInfo.value = data;
      addLog('获取用户信息成功!');
      addLog(`用户信息: ${JSON.stringify(data)}`);
    }
  } catch (err: any) {
    addLog(`获取用户信息异常: ${err.message || '网络错误'}`);
  }
}

// 复制结果到剪贴板
async function copyResult() {
  const result = {
    loginResult: loginResult.value,
    userInfo: userInfo.value,
    logs: logs.value
  };
  
  try {
    await navigator.clipboard.writeText(JSON.stringify(result, null, 2));
    addLog('测试结果已复制到剪贴板');
  } catch (err) {
    addLog('复制失败，请手动复制');
  }
}
</script>

<template>
  <div class="login-test-container">
    <NCard title="登录接口测试" class="test-card">
      <template #header-extra>
        <NSpace>
          <NButton @click="clearLogs" size="small">清空日志</NButton>
          <NButton @click="copyResult" size="small" type="info">复制结果</NButton>
        </NSpace>
      </template>
      
      <!-- 登录表单 -->
      <NForm :model="formData" label-placement="left" label-width="80">
        <NFormItem label="用户名">
          <NInput 
            v-model:value="formData.userName" 
            placeholder="请输入用户名"
            :disabled="loading"
          />
        </NFormItem>
        <NFormItem label="密码">
          <NInput 
            v-model:value="formData.password" 
            type="password" 
            placeholder="请输入密码"
            :disabled="loading"
            @keyup.enter="testLogin"
          />
        </NFormItem>
        <NFormItem>
          <NButton 
            type="primary" 
            :loading="loading" 
            @click="testLogin"
            block
          >
            测试登录
          </NButton>
        </NFormItem>
      </NForm>

      <!-- 错误信息 -->
      <NAlert v-if="errorMessage" type="error" :title="errorMessage" class="mb-4" />

      <!-- 登录结果 -->
      <div v-if="loginResult" class="result-section">
        <h4>登录结果:</h4>
        <NCode :code="JSON.stringify(loginResult, null, 2)" language="json" />
      </div>

      <!-- 用户信息 -->
      <div v-if="userInfo" class="result-section">
        <h4>用户信息:</h4>
        <NCode :code="JSON.stringify(userInfo, null, 2)" language="json" />
      </div>

      <!-- 日志 -->
      <div v-if="logs.length > 0" class="result-section">
        <h4>测试日志:</h4>
        <NLog :log="logs.join('\n')" :rows="10" />
      </div>
    </NCard>

    <!-- API信息 -->
    <NCard title="API信息" class="api-info-card">
      <div class="api-info">
        <p><strong>后端地址:</strong> http://192.168.1.66:8080</p>
        <p><strong>登录接口:</strong> POST /api/auth/login</p>
        <p><strong>用户信息接口:</strong> GET /api/auth/getUserInfo</p>
        <p><strong>测试账号:</strong> testuser</p>
        <p><strong>测试密码:</strong> 123456</p>
      </div>
    </NCard>
  </div>
</template>

<style lang="scss" scoped>
.login-test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  
  .test-card {
    margin-bottom: 20px;
  }
  
  .result-section {
    margin-top: 16px;
    
    h4 {
      margin-bottom: 8px;
      color: #333;
    }
  }
  
  .api-info-card {
    .api-info {
      p {
        margin: 8px 0;
        
        strong {
          color: #2080f0;
        }
      }
    }
  }
}
</style>
