# 登录接口对接文档

## 概述

本文档记录了与后端登录接口的对接过程，确保了TypeScript类型安全和完整的错误处理。

## 后端配置

- **后端地址**: http://************:8080
- **接口文档**: http://************:8080/doc/api/swagger/index.html
- **测试账号**: testuser
- **测试密码**: 123456

## 配置更改

### 1. 环境变量配置 (.env)

```bash
# 后端服务基础URL
VITE_SERVICE_BASE_URL=http://************:8080

# 其他服务基础URL配置
VITE_OTHER_SERVICE_BASE_URL={}
```

### 2. API接口更新 (src/service/api/auth.ts)

#### 登录接口
- **路径**: `/api/auth/login`
- **方法**: POST
- **参数类型**: `Api.Auth.LoginParams`
- **返回类型**: `Api.Auth.LoginToken`

#### 获取用户信息接口
- **路径**: `/api/auth/getUserInfo`
- **方法**: GET
- **返回类型**: `Api.Auth.UserInfo`

#### 刷新Token接口
- **路径**: `/api/auth/refreshToken`
- **方法**: POST
- **参数类型**: `Api.Auth.RefreshTokenParams`
- **返回类型**: `Api.Auth.LoginToken`

### 3. TypeScript类型定义 (src/typings/api.d.ts)

```typescript
namespace Api {
  namespace Auth {
    /** 登录请求参数 */
    interface LoginParams {
      userName: string;
      password: string;
    }

    /** 登录响应数据 */
    interface LoginToken {
      /** 访问token */
      token: string;
      /** 刷新token */
      refreshToken: string;
    }

    /** 用户信息 */
    interface UserInfo {
      /** 用户ID */
      userId: string;
      /** 用户名 */
      userName: string;
      /** 用户角色列表 */
      roles: string[];
      /** 用户权限按钮列表 */
      buttons: string[];
    }

    /** 刷新token请求参数 */
    interface RefreshTokenParams {
      refreshToken: string;
    }
  }
}
```

### 4. 请求配置更新 (src/service/request/index.ts)

- 移除了测试用的 `apifoxToken`
- 设置了正确的 `Content-Type: application/json`
- 保持了完整的认证和错误处理逻辑

### 5. 认证Store更新 (src/store/modules/auth/index.ts)

- 更新了 `login` 函数以使用新的类型安全接口
- 保持了完整的登录流程和错误处理

## 测试功能

### 测试页面
- **路径**: `/test/login-test`
- **组件**: `src/components/test/LoginTest.vue`
- **页面**: `src/views/test/login-test/index.vue`

### 测试功能包括:
1. **登录测试**: 使用提供的测试账号进行登录
2. **用户信息获取**: 登录成功后自动获取用户信息
3. **实时日志**: 显示详细的请求和响应日志
4. **错误处理**: 完整的错误信息展示
5. **结果复制**: 可以复制测试结果到剪贴板

### 使用方法
1. 启动开发服务器: `pnpm dev`
2. 访问测试页面: http://localhost:9529/test/login-test
3. 使用默认账号密码或自定义账号进行测试
4. 查看详细的请求日志和响应数据

## 类型安全特性

### 1. 编译时类型检查
- 所有API调用都有完整的TypeScript类型约束
- 参数类型错误会在编译时被捕获
- IDE提供完整的代码提示和自动补全

### 2. 运行时类型验证
- 请求参数自动验证
- 响应数据类型安全
- 错误处理类型化

### 3. 向后兼容
- 提供了兼容旧版本的函数 (`fetchLoginLegacy`, `fetchRefreshTokenLegacy`)
- 渐进式迁移支持

## 示例代码

### 基本使用
```typescript
import { fetchLogin } from '@/service/api/auth';

// 类型安全的登录
const loginParams: Api.Auth.LoginParams = {
  userName: 'testuser',
  password: '123456'
};

const { data, error } = await fetchLogin(loginParams);
```

### 完整登录流程
```typescript
import { useAuthStore } from '@/store/modules/auth';

const authStore = useAuthStore();

// 使用store进行登录
await authStore.login('testuser', '123456');
```

## 错误处理

### 网络错误
- 连接超时
- 网络不可达
- 服务器错误

### 业务错误
- 用户名或密码错误
- 账号被锁定
- Token过期

### 类型错误
- 参数类型不匹配
- 响应数据格式错误

## 安全考虑

1. **Token管理**: 自动处理token的存储和刷新
2. **请求拦截**: 自动添加认证头
3. **响应拦截**: 自动处理token过期和错误
4. **类型安全**: 防止参数注入和数据泄露

## 下一步

1. 根据实际后端API调整接口路径和参数
2. 完善错误处理和用户提示
3. 添加更多的单元测试
4. 优化用户体验和性能

## 文件清单

### 核心文件
- `src/service/api/auth.ts` - 认证API接口
- `src/typings/api.d.ts` - TypeScript类型定义
- `src/store/modules/auth/index.ts` - 认证状态管理
- `src/service/request/index.ts` - 请求配置

### 测试文件
- `src/components/test/LoginTest.vue` - 登录测试组件
- `src/views/test/login-test/index.vue` - 测试页面
- `src/service/api/__tests__/auth.test.ts` - 单元测试
- `src/examples/auth-usage.ts` - 使用示例

### 配置文件
- `.env` - 环境变量配置
- `src/locales/langs/zh-cn.ts` - 中文国际化
- `src/locales/langs/en-us.ts` - 英文国际化
