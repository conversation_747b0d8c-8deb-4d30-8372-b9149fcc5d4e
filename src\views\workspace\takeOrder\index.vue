<script lang="ts" setup>
import { h, ref } from 'vue';
import { useBoolean } from '@sa/hooks';

defineOptions({
  name: 'TakeOrder'
});

// 弹出框控制
const { bool: modalVisible, setTrue: openModal, setFalse: closeModal } = useBoolean();

// 表单数据
interface LogisticsForm {
  logisticsNumber: string;
  purchaseAmount: number | null;
}

const formData = ref<LogisticsForm>({
  logisticsNumber: '',
  purchaseAmount: null
});

// 表格数据类型
interface OrderData {
  id: string;
  platformInfo: string;
  businessPlatformInfo: string;
  buyerInfo: string;
  productDetails: string;
  creator: string;
  createTime: string;
}

// 模拟表格数据
const tableData = ref<OrderData[]>([
  {
    id: '1',
    platformInfo: '淘宝',
    businessPlatformInfo: '天猫旗舰店',
    buyerInfo: '张三 (138****1234)',
    productDetails: 'iPhone 15 Pro 256GB 深空黑色',
    creator: '管理员',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    platformInfo: '京东',
    businessPlatformInfo: '京东自营',
    buyerInfo: '李四 (139****5678)',
    productDetails: 'MacBook Pro 14英寸 M3芯片',
    creator: '操作员A',
    createTime: '2024-01-15 11:45:00'
  },
  {
    id: '3',
    platformInfo: '拼多多',
    businessPlatformInfo: '品牌官方店',
    buyerInfo: '王五 (136****9012)',
    productDetails: 'AirPods Pro 第二代',
    creator: '操作员B',
    createTime: '2024-01-15 14:20:00'
  }
]);

// 表格列定义
const columns = [
  {
    title: '平台信息',
    key: 'platformInfo',
    width: 120
  },
  {
    title: '经营平台信息',
    key: 'businessPlatformInfo',
    width: 150
  },
  {
    title: '买家信息',
    key: 'buyerInfo',
    width: 180
  },
  {
    title: '商品详情',
    key: 'productDetails',
    width: 200
  },
  {
    title: '创建人',
    key: 'creator',
    width: 100
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'operate',
    width: 180,
    render: (row: OrderData) => {
      return h('div', { class: 'flex gap-8px flex-wrap' }, [
        h(
          'NButton',
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleEdit(row.id)
          },
          '编辑'
        ),
        h(
          'NButton',
          {
            size: 'small',
            type: 'warning',
            ghost: true,
            onClick: () => handleInvalidate(row.id)
          },
          '失效'
        ),
        h(
          'NButton',
          {
            size: 'small',
            type: 'error',
            ghost: true,
            onClick: () => handleDelete(row.id)
          },
          '删除'
        )
      ]);
    }
  }
];

// 上传物流单
function handleUploadLogistics() {
  openModal();
}

// 提交物流单
function handleSubmitLogistics() {
  if (!formData.value.logisticsNumber || !formData.value.purchaseAmount) {
    window.$message?.warning('请填写完整信息');
    return;
  }

  window.$message?.success('物流单上传成功');

  // 重置表单
  formData.value = {
    logisticsNumber: '',
    purchaseAmount: null
  };

  closeModal();
}

// 编辑操作
function handleEdit(id: string) {
  window.$message?.info(`编辑订单 ${id}`);
}

// 失效操作
function handleInvalidate(id: string) {
  window.$dialog?.warning({
    title: '确认失效',
    content: `确定要将订单 ${id} 设为失效状态吗？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      window.$message?.success(`订单 ${id} 已设为失效状态`);
      // 这里可以添加实际的失效逻辑，比如更新订单状态
      // 例如：更新数据库中的订单状态为失效
    }
  });
}

// 删除操作
function handleDelete(id: string) {
  window.$dialog?.warning({
    title: '确认删除',
    content: `确定要删除订单 ${id} 吗？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = tableData.value.findIndex(item => item.id === id);
      if (index > -1) {
        tableData.value.splice(index, 1);
        window.$message?.success('删除成功');
      }
    }
  });
}

// 刷新数据
function handleRefresh() {
  window.$message?.info('刷新数据');
}
</script>

<template>
  <div class="p-16px">
    <!-- 页面标题和操作按钮 -->
    <div class="mb-16px flex items-center justify-between">
      <h2 class="text-18px font-semibold">订单列表</h2>
      <div class="flex gap-12px">
        <NButton type="primary" @click="handleUploadLogistics">
          <template #icon>
            <icon-ic-round-upload />
          </template>
          上传物流单
        </NButton>
        <NButton @click="handleRefresh">
          <template #icon>
            <icon-mdi-refresh />
          </template>
          刷新
        </NButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NCard :bordered="false" class="card-wrapper">
      <NDataTable
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :bordered="false"
        size="small"
        class="sm:h-full"
      />
    </NCard>

    <!-- 上传物流单弹出框 -->
    <NModal
      v-model:show="modalVisible"
      preset="card"
      title="上传物流单"
      class="w-500px"
      :segmented="{ footer: 'soft' }"
    >
      <NForm :model="formData" label-placement="left" label-width="100px" require-mark-placement="right-hanging">
        <NFormItem label="物流单号" required>
          <NInput v-model:value="formData.logisticsNumber" placeholder="请输入物流单号" clearable />
        </NFormItem>
        <NFormItem label="采购金额" required>
          <NInputNumber
            v-model:value="formData.purchaseAmount"
            placeholder="请输入采购金额"
            class="w-full"
            :precision="2"
            :min="0"
          >
            <template #suffix>元</template>
          </NInputNumber>
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" @click="handleSubmitLogistics">确认提交</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<style lang="scss">
.card-wrapper {
  min-height: 500px;
}
</style>
